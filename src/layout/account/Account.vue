<template>
	<account-menu>
		<router-view v-if="isUserFullyActive || isUserActive" />
		<user-inactive v-else-if="isUserInActive" />
		<banned v-else-if="isUserBanned" />
	</account-menu>
</template>

<script>
import AccountMenu from './AccountMenu';
import UserInactive from './UserInactive.vue';
import Banned from './Banned.vue';
import store from '../../store/store';
import authService from "../../services/auth.service";
import { isActive, isInactive, isBanned } from "@/constants/userStatus";
import userStatus from "../../constants/userStatus";
import { performAction } from '@/helpers/common';
import { STORAGE_ACTION_KEYS } from '@/constants/constants';

export default {
	data() {
		return {
			user: store.state.userProfile,
		};
	},
	components: {
		AccountMenu,
		UserInactive,
		Banned,
	},

	async mounted() {
		await this.refreshUserProfile(true);
		await store.dispatch('getReferralCode');
		await store.dispatch('setInitialBalance', -1);
		performAction(STORAGE_ACTION_KEYS.GET_BANKS, async () => {
            await store.dispatch("getAvailableBanks");
        }, 60 * 1000)
	},

	methods: {
		async refreshUserProfile(showLoading = false) {
			const res = await authService.getUserProfile(showLoading);
			if (res && res.data) {
				await store.dispatch('setUserProfile', res.data);
				this.user = res.data;
			}
		},
	},

	computed: {
		completedEmail() {
			return this.user.email
				|| this.user.email_apple
				|| this.user.email_fb
				|| this.user.email_google;
		},

		completedPhone() {
			return this.user.phone && this.user.country_code;
		},

		completedSSN() {
			if (this.user.country) {
				if (this.user.country.code === 'US') {
					return this.user.ssn;
				}
			}
			return true;
		},

		completedPersonalInfo() {
			return this.user.name
				&& this.completedEmail
				&& this.completedPhone
				&& this.user.country
				&& this.user.street_address
				&& this.user.city
				&& this.user.state
				&& this.user.zip_code
				&& this.user.dob
				&& this.completedSSN;
		},

		step() {
			if (!this.completedPersonalInfo) {
				return 1;
			}
			return 2;
		},

		isUserInActive() {
			return isInactive();
		},

		isUserFullyActive() {
			return store.state.userProfile && store.state.userProfile.status === userStatus.FullyActive;
		},

		isUserActive() {
			return isActive();
		},

		isUserBanned() {
			return isBanned();
		},
	},
}
</script>

<style lang="scss">
.account {
	transition: 0.3s ease;
	.sidebar-overlay {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background-color: #000;
		opacity: 0.5;
		z-index: 900;
	}
}
</style>
