<template>
  <div class="settings" ref="kyc">
    <div class="row justify-content-center">
      <div class="col-12 col-lg-12 mt-2">
        <p class="font-28 font-weight-bold">
          {{ $t('account.KYC_TITLE') }}
        </p>
        <div class="content col-12 d-flex flex-column">
          <div>
            <KycPhase2Steps v-if="currentStep < 5" :current-step="currentStep" class="mb-4"/>
            <InputJobInfo v-if="currentStep === 1" :model-value="requestBody" @update:modelValue="onJobInfoChange"
                          :occupations="occupations" :job-positions="jobPositions" :monthly-incomes="monthlyIncomes"
                          :source-of-incomes="sourceOfIncomes" :industry-sectors="industrySectors" @next-step="nextStep"/>
            <InputAdditionalStatement v-if="currentStep === 2" :model-value="requestBody" @update:modelValue="onAdditionalStatementChange"
                                      :admin-flags="adminFlags" :job-info-flags="jobInfoFlags" @previous-step="currentStep = 1" @next-step="nextStep"/>
            <DataConfirmation v-if="currentStep === 3" :model-value="requestBody"
                              :occupations="occupations" :job-positions="jobPositions"
                              :monthly-incomes="monthlyIncomes" :source-of-incomes="sourceOfIncomes"
                              :industry-sectors="industrySectors" @previous-step="currentStep = 2" @next-step="nextStep"/>
            <KycPhase2Status v-if="currentStep === 4"/>
          </div>
        </div>
      </div>
    </div>
    <popup ref="popupKycPhase1" @on-positive-clicked="openKycPhase1"></popup>
  </div>
</template>

<script>
import accountService from "@/services/account.service";
import KycPhase2Steps from "@/components/kycPhase2/KycPhase2Steps.vue";
import InputJobInfo from "@/components/kycPhase2/InputJobInfo.vue";
import InputAdditionalStatement from "@/components/kycPhase2/InputAdditionalStatement.vue";
import DataConfirmation from "@/components/kycPhase2/DataConfirmation.vue";
import KycPhase2Status from "@/components/kycPhase2/KycPhase2Status.vue";
import { KYC_PHASE_2_STATUS } from "@/constants/constants";
import { isCompletedKyc, isCompletedKycPhase2, isFullyActive } from "@/constants/userStatus";
import Popup from "@/components/Popup.vue";


export default {
  components: {
    Popup,
    KycPhase2Steps,
    InputJobInfo,
    InputAdditionalStatement,
    DataConfirmation,
    KycPhase2Status,
  },

  data() {
    return {
      occupations: [],
      jobPositions: [],
      monthlyIncomes: [],
      sourceOfIncomes: [],
      industrySectors: [],
      adminFlags: [],
      jobInfoFlags: [],
      requestBody: {
        occupation_id: null,
        job_position_id: null,
        monthly_income_id: null,
        source_of_income_id: null,
        company_name: null,
        company_address: null,
        industry_sector_id: null,
        flags: [],
      },
      currentStep: 1,
    };
  },

  async mounted() {
    await this.getKycPhase2Info()
  },

  computed: {
    userProfile() {
      return this.$store.getters.userProfile;
    },
  },

  methods: {
    async getKycPhase2Info() {
      if (isCompletedKycPhase2()) {
        this.currentStep = 5
      } else {
        const res = await accountService.getKycPhase2Info()
        if (res && res.data) {
          const nextUserProfile = { ...this.userProfile, kyc_phase_2: res.data }
          await this.$store.dispatch('setUserProfile', nextUserProfile)
          if (nextUserProfile.kyc_phase_2) {
            if (nextUserProfile.kyc_phase_2.status === KYC_PHASE_2_STATUS.STATUS_COMPLETED) {
              this.currentStep = 5
            } else {
              this.currentStep = 4
            }
          } else {
            this.currentStep = 1
            await this.getKycPhase2Config()
          }
        } else {
          await this.getKycPhase2Config()
        }
      }
    },
    async getKycPhase2Config() {
      const res = await accountService.getKycPhase2Config()
      if (res && res.data) {
        this.occupations = res.data.occupations
        this.jobPositions = res.data.job_positions
        this.monthlyIncomes = res.data.monthly_incomes
        this.sourceOfIncomes = res.data.source_of_incomes
        this.industrySectors = res.data.industry_sectors
        this.adminFlags = res.data.flags
      }
    },
    openKycPhase1Popup() {
      this.$refs.popupKycPhase1.openPopup({
        title: this.$t("account.PLEASE_COMPLETE_KYC_PHASE_1_FIRST"),
        positiveButton: this.$t("PAYMENT.CONTINUE_TO_KYC")
      })
    },
    async openKycPhase1() {
      await this.$router.push({ path: "/account/my-account/kyc", query: { redirect: this.$route.fullPath } })
    },
    nextStep: async function () {
      if (this.currentStep < 3) {
        this.currentStep++
      } else if (this.currentStep === 3) {
        await this.submitKycPhase2Info()
      }
    },
    computeJobInfoFlags(payload) {
      const flags = new Set()
      if (payload.occupation_id != null) {
        const occ = this.occupations.find(o => o.id === payload.occupation_id)
        if (occ?.flag) flags.add(occ.flag)
      }
      if (payload.industry_sector_id != null) {
        const ind = this.industrySectors.find(i => i.id === payload.industry_sector_id)
        if (ind?.flag) flags.add(ind.flag)
      }
      return Array.from(flags)
    },
    onJobInfoChange(jobInfo) {
      this.jobInfoFlags = this.computeJobInfoFlags(jobInfo)
      const current = Array.isArray(this.requestBody.flags) ? this.requestBody.flags : []
      const merged = Array.from(new Set([...current, ...this.jobInfoFlags, ...this.adminFlags]))
      this.requestBody = { ...this.requestBody, ...jobInfo, flags: merged }
    },
    async onAdditionalStatementChange(additionalStatement) {
      this.requestBody = { ...this.requestBody, ...additionalStatement };
    },
    async submitKycPhase2Info() {
      if (isCompletedKyc() || isFullyActive(true)) {
        const res = await accountService.submitKycPhase2Info(this.requestBody)
        if (res && res.data) {
          const nextUserProfile = { ...this.userProfile, kyc_phase_2: res.data }
          await this.$store.dispatch('setUserProfile', nextUserProfile)
          this.currentStep = 4
        }
      } else {
        this.openKycPhase1Popup()
      }
    }
  },
}
</script>

<style lang="scss">
.settings {
  width: 100%;
  margin-top: 20px;
  padding-bottom: 60px;

  .content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 20px;
    margin-top: 15px;
  }
}
</style>
