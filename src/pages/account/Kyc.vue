<template>
  <div class="settings" ref="kyc">
    <div class="row justify-content-center">
      <div class="col-12 col-lg-12 mt-2">
        <p class="font-28 font-weight-bold">
          {{ $t('account.KYC_TITLE') }}
        </p>
        <div class="content col-12 d-flex flex-column">
          <div>
            <KycSteps v-if="currentStep < 4" :current-step="currentStep" class="mb-4"/>
            <UploadCard v-if="currentStep === 1" @next-step="nextStep"/>
            <InputCardInfo v-if="currentStep === 2" @next-step="nextStep"/>
            <TakeSelfie v-if="currentStep === 3" @next-step="nextStep"/>
            <KycStatus v-if="currentStep === 4"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import authService from "@/services/auth.service";
import KycSteps from "@/components/kyc/KycSteps";
import UploadCard from '@/components/kyc/UploadCard';
import TakeSelfie from '@/components/kyc/TakeSelfie';
import InputCardInfo from '@/components/kyc/InputCardInfo';
import KycStatus from '@/components/kyc/KycStatus';
import store from '@/store/store';
import externalSites from '@/constants/externalSites';

export default {

  components: {
    KycSteps,
    UploadCard,
    TakeSelfie,
    InputCardInfo,
    KycStatus,
  },

  data() {
    return {
      // user: store.state.userProfile,
      contact: externalSites.CUSTOMER_EMAIL,
      contactMailTo: externalSites.MAIL_TO.CUSTOMER_SUPPORT,
      currentStep: 1,
    };
  },

  mounted() {
    this.currentStep = this.getCurrentStep
  },

  computed: {
    user() {
      return this.$store.getters.userProfile;
    },
    completedUpload() {
      return this.user.kyc_status && this.user.kyc_status.completedUpload;
    },
    completedIDVerification() {
      return this.user.kyc_status && this.user.kyc_status.completedIDVerification;
    },
    completedSelfie() {
      return this.user.kyc_status && this.user.kyc_status.completedSelfie;
    },
    getCurrentStep() {
      if (this.completedSelfie) {
        return 4
      }
      if (this.completedIDVerification) {
        return 3
      }
      if (this.completedUpload) {
        return 2
      }
      return 1
    },
  },

  methods: {
    async onUpdateSuccess() {
      const res = await authService.getUserProfile(false);
      if (res && res.data) {
        await store.dispatch('setUserProfile', res.data);
        this.user = res.data;
      }
    },
    async nextStep() {
      await this.onUpdateSuccess()
      this.currentStep++
    },
  },
}
</script>

<style lang="scss">
.settings {
  width: 100%;
  margin-top: 20px;
  padding-bottom: 60px;

  .content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 20px;
    margin-top: 15px;

    .verified {
      font-size: 28px;
      font-weight: 700;
      color: var(--primary-color);
    }
  }

  .success-img {
    width: 150px;
    height: 150px;
    object-fit: contain;
  }
}
</style>
