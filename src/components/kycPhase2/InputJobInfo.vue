<template>
  <b-container class="job-info-container cls-kyc-form-container">
    <b-row align-h="center">
      <b-col cols="12" lg="11">
        <div class="d-flex flex-column flex-lg-row">
          <b-col cols="12" lg="6" class="text-center w-100 d-flex flex-column justify-content-center align-items-center cls-info-block">
            <img src="@/assets/img/new-info-person.png" alt="" class="icon-img">
            <p class="font-28 font-weight-bold title mt-4">{{ $t('account.ENTER_YOUR_JOB') }}</p>
            <p class="description font-16 mt-1">{{ $t('account.ENTER_YOUR_JOB_DESCRIPTION') }}</p>
          </b-col>

          <b-col cols="12" lg="6" class="mt-3 w-100">
            <Form ref="observer" class="form col-md-12" v-slot="{ handleSubmit }">
              <b-form id="job-info-form" ref="form" @submit.prevent="handleSubmit(submitJobInfo)" class="cls-custom-form-group-field extend-invalid">
                <!-- OCCUPATION -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.occupation_id"
                    @update:modelValue="updateField('occupation_id', $event)"
                    :name="$t('account.OCCUPATION')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.OCCUPATION')" label-for="KYC_OCCUPATION" label-class="custom-label" class="custom-form-group">
                      <b-form-select
                        id="KYC_OCCUPATION"
                        :value="form.occupation_id"
                        v-bind="ctx.field"
                        aria-describedby="input-occupation-feedback"
                        :state="getValidationState(ctx.meta)"
                      >
                        <option :value="null" disabled>{{ $t('account.OCCUPATION') }}</option>
                        <option v-for="occupation in occupations" :key="occupation.id" :value="occupation.id">
                          {{ occupation.name }}
                        </option>
                      </b-form-select>
                    </b-form-group>
                    <b-form-invalid-feedback id="input-occupation-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- JOB POSITION -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.job_position_id"
                    @update:modelValue="updateField('job_position_id', $event)"
                    :name="$t('account.JOB_POSITION')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.JOB_POSITION')" label-for="KYC_JOB_POSITION" label-class="custom-label" class="custom-form-group">
                      <b-form-select
                        id="KYC_JOB_POSITION"
                        :value="form.job_position_id"
                        v-bind="ctx.field"
                        aria-describedby="input-job-position-feedback"
                        :state="getValidationState(ctx.meta)"
                      >
                        <option :value="null" disabled>{{ $t('account.JOB_POSITION') }}</option>
                        <option v-for="jp in jobPositions" :key="jp.id" :value="jp.id">
                          {{ jp.name }}
                        </option>
                      </b-form-select>
                    </b-form-group>
                    <b-form-invalid-feedback id="input-job-position-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- MONTHLY INCOME -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.monthly_income_id"
                    @update:modelValue="updateField('monthly_income_id', $event)"
                    :name="$t('account.MONTHLY_INCOME')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.MONTHLY_INCOME')" label-for="KYC_MONTHLY_INCOME" label-class="custom-label" class="custom-form-group">
                      <b-form-select
                        id="KYC_MONTHLY_INCOME"
                        :value="form.monthly_income_id"
                        v-bind="ctx.field"
                        aria-describedby="input-monthly-income-feedback"
                        :state="getValidationState(ctx.meta)"
                      >
                        <option :value="null" disabled>{{ $t('account.MONTHLY_INCOME') }}</option>
                        <option v-for="mi in monthlyIncomes" :key="mi.id" :value="mi.id">
                          {{ mi.name }}
                        </option>
                      </b-form-select>
                    </b-form-group>
                    <b-form-invalid-feedback id="input-monthly-income-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- SOURCE OF INCOME -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.source_of_income_id"
                    @update:modelValue="updateField('source_of_income_id', $event)"
                    :name="$t('account.SOURCE_OF_INCOME')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.SOURCE_OF_INCOME')" label-for="KYC_SOURCE_OF_INCOME" label-class="custom-label" class="custom-form-group">
                      <b-form-select
                        id="KYC_SOURCE_OF_INCOME"
                        :value="form.source_of_income_id"
                        v-bind="ctx.field"
                        aria-describedby="input-source-income-feedback"
                        :state="getValidationState(ctx.meta)"
                      >
                        <option :value="null" disabled>{{ $t('account.SOURCE_OF_INCOME') }}</option>
                        <option v-for="soi in sourceOfIncomes" :key="soi.id" :value="soi.id">
                          {{ soi.name }}
                        </option>
                      </b-form-select>
                    </b-form-group>
                    <b-form-invalid-feedback id="input-source-income-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- COMPANY NAME -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.company_name"
                    @update:modelValue="updateField('company_name', $event)"
                    :name="$t('account.COMPANY_BUSINESS_NAME')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.COMPANY_BUSINESS_NAME')" label-for="ACCOUNT_COMPANY_BUSINESS_NAME" label-class="custom-label" class="custom-form-group">
                      <b-form-input
                        id="ACCOUNT_COMPANY_BUSINESS_NAME"
                        v-bind="ctx.field"
                        aria-describedby="input-company-name-feedback"
                        :state="getValidationState(ctx.meta)"
                      />
                    </b-form-group>
                    <b-form-invalid-feedback id="input-company-name-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- COMPANY ADDRESS -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.company_address"
                    @update:modelValue="updateField('company_address', $event)"
                    :name="$t('account.COMPANY_BUSINESS_ADDRESS')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.COMPANY_BUSINESS_ADDRESS')" label-for="ACCOUNT_COMPANY_BUSINESS_ADDRESS" label-class="custom-label" class="custom-form-group">
                      <b-form-input
                        id="ACCOUNT_COMPANY_BUSINESS_ADDRESS"
                        v-bind="ctx.field"
                        aria-describedby="input-company-address-feedback"
                        :state="getValidationState(ctx.meta)"
                      />
                    </b-form-group>
                    <b-form-invalid-feedback id="input-company-address-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>

                <!-- INDUSTRY -->
                <div class="form-group-parent">
                  <Field
                    :key="$i18n.locale"
                    :model-value="form.industry_sector_id"
                    @update:modelValue="updateField('industry_sector_id', $event)"
                    :name="$t('account.INDUSTRY')"
                    :rules="{ required: true }"
                    v-slot="ctx"
                  >
                    <b-form-group :label="$t('account.INDUSTRY')" label-for="KYC_INDUSTRY" label-class="custom-label" class="custom-form-group">
                      <b-form-select
                        id="KYC_INDUSTRY"
                        :value="form.industry_sector_id"
                        v-bind="ctx.field"
                        aria-describedby="input-industry-feedback"
                        :state="getValidationState(ctx.meta)"
                      >
                        <option :value="null" disabled>{{ $t('account.INDUSTRY') }}</option>
                        <option v-for="ind in industrySectors" :key="ind.id" :value="ind.id">
                          {{ ind.name }}
                        </option>
                      </b-form-select>
                    </b-form-group>
                    <b-form-invalid-feedback id="input-industry-feedback">
                      {{ ctx.errors[0] }}
                    </b-form-invalid-feedback>
                  </Field>
                </div>
              </b-form>
            </Form>
          </b-col>
        </div>
      </b-col>
    </b-row>

    <div class="d-flex flex-row justify-content-end mt-4">
      <b-button class="btn-main px-4" type="submit" variant="none" form="job-info-form">
        {{ $t('common.NEXT') }}
      </b-button>
    </div>
  </b-container>
</template>

<script>
import { Field, Form } from 'vee-validate'

export default {
  name: 'InputJobInfo',
  components: { Field, Form },
  emits: ['update:modelValue', 'next-step'],
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        occupation_id: null,
        job_position_id: null,
        monthly_income_id: null,
        source_of_income_id: null,
        company_name: null,
        company_address: null,
        industry_sector_id: null,
      }),
    },
    occupations: { type: Array, default: () => [] },
    jobPositions: { type: Array, default: () => [] },
    monthlyIncomes: { type: Array, default: () => [] },
    sourceOfIncomes: { type: Array, default: () => [] },
    industrySectors: { type: Array, default: () => [] },
  },

  computed: {
    form: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },

  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    updateField(field, value) {
      if (this.form[field] !== value) {
        this.form = { ...this.form, [field]: value }
      }
    },
    submitJobInfo() {
      // Validation already passed by vee-validate
      this.$emit('next-step')
    },
  },
}
</script>

<style lang="scss">
.job-info-container {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  * {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }

  .title {
    font-size: 32px;
    font-weight: 700;
    color: #3E3E3E;
  }

  .description {
    color: #616161;
    white-space: pre;
  }

  .icon-img {
    width: 180px;
    height: 100px;
    object-fit: contain;
  }
}
</style>
