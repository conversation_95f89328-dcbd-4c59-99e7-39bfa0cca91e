<template>
  <b-container class="occupation-container cls-kyc-form-container">
    <b-row align-h="center">
      <b-col cols="12">
        <div class="d-flex flex-column flex-lg-row">
          <b-col cols="12" class="text-center w-100 d-flex flex-column justify-content-center align-items-center cls-info-block">
            <p class="font-28 font-weight-bold title mt-4">
              {{ $t('account.ADDITIONAL_STATEMENT') }}
            </p>
            <p class="description font-16 mt-2">
              {{ $t('account.ADDITIONAL_STATEMENT_DESCRIPTION') }}
            </p>
          </b-col>
        </div>
      </b-col>
    </b-row>

    <b-row class="mt-4">
      <b-col cols="12" md="10" lg="8" class="mx-auto px-0">
        <div
          class="flag-row d-flex align-items-center mb-3"
          role="button"
          :tabindex="pepLocked ? -1 : 0"
          :aria-pressed="pepSelected"
          :aria-disabled="pepLocked"
          @click="toggleFlag(FLAGS.PEP)"
          @keydown.enter.prevent="toggleFlag(FLAGS.PEP)"
          @keydown.space.prevent="toggleFlag(FLAGS.PEP)"
        >
          <div class="check-col">
            <img :src="pepSelected ? checkOn : checkOff" alt="" class="check-img" draggable="false"/>
          </div>
          <div class="flag-card color-gray-6d p-3 flex-1">
            <div class="flag-title mb-1">
              {{ $t('account.POLITICALLY_EXPOSED_PERSON') }}
            </div>
            <div class="flag-description text-muted color-gray-6d">
              {{ $t('account.POLITICALLY_EXPOSED_PERSON_DESCRIPTION') }}
            </div>
          </div>
        </div>

        <div
          class="flag-row d-flex align-items-center mb-3"
          role="button"
          :tabindex="boLocked ? -1 : 0"
          :aria-pressed="boSelected"
          :aria-disabled="boLocked"
          @click="toggleFlag(FLAGS.BO)"
          @keydown.enter.prevent="toggleFlag(FLAGS.BO)"
          @keydown.space.prevent="toggleFlag(FLAGS.BO)"
        >
          <div class="check-col">
            <img :src="boSelected ? checkOn : checkOff" alt="" class="check-img" draggable="false"/>
          </div>
          <div class="flag-card color-gray-6d p-3 flex-1">
            <div class="flag-title mb-1">
              {{ $t('account.BENEFICIAL_OWNER') }}
            </div>
            <div class="flag-description text-muted color-gray-6d">
              {{ $t('account.BENEFICIAL_OWNER_DESCRIPTION') }}
            </div>
          </div>
        </div>
      </b-col>
    </b-row>

    <div class="btn-row-eq mt-4 mb-2">
      <b-button class="btn-outline-main px-4" @click="previousStep" variant="none">
        {{ $t('common.PREVIOUS') }}
      </b-button>
      <b-button class="btn-main px-4" variant="none" @click="nextStep">
        {{ $t('common.NEXT') }}
      </b-button>
    </div>
  </b-container>
</template>

<script>
import { KYC_PHASE_2_FLAGS } from "@/constants/constants"
import checkOn from "@/assets/img/check-completed.svg"
import checkOff from "@/assets/img/check-incompleted.svg"

export default {
  name: 'InputAdditionalStatement',
  emits: ['previous-step', 'next-step', 'update:modelValue'],
  props: {
    adminFlags: { type: Array, default: () => [] },
    jobInfoFlags: { type: Array, default: () => [] },
    // Parent passes full requestBody; we update its `flags` directly on toggle.
    modelValue: {
      type: Object,
      default: () => ({ flags: [] }),
    },
  },

  data() {
    return { checkOn, checkOff }
  },

  computed: {
    FLAGS() {
      return KYC_PHASE_2_FLAGS
    },
    pepLocked() {
      return this.adminFlags.includes(this.FLAGS.PEP) || this.jobInfoFlags.includes(this.FLAGS.PEP)
    },
    boLocked() {
      return this.adminFlags.includes(this.FLAGS.BO) || this.jobInfoFlags.includes(this.FLAGS.BO)
    },
    pepSelected() {
      return this.pepLocked || (Array.isArray(this.modelValue.flags) && this.modelValue.flags.includes(this.FLAGS.PEP))
    },
    boSelected() {
      return this.boLocked || (Array.isArray(this.modelValue.flags) && this.modelValue.flags.includes(this.FLAGS.BO))
    },
  },

  methods: {
    previousStep() {
      this.$emit('previous-step')
    },
    nextStep() {
      this.$emit('next-step')

    },
    toggleFlag(flag) {
      if ((flag === this.FLAGS.PEP && this.pepLocked) || (flag === this.FLAGS.BO && this.boLocked)) return

      const current = Array.isArray(this.modelValue.flags) ? this.modelValue.flags.slice() : []
      const set = new Set(current)

      if (set.has(flag)) set.delete(flag)
      else set.add(flag)

      const nextFlags = []
      if (set.has(this.FLAGS.PEP)) nextFlags.push(this.FLAGS.PEP)
      if (set.has(this.FLAGS.BO)) nextFlags.push(this.FLAGS.BO)

      this.$emit('update:modelValue', { ...this.modelValue, flags: nextFlags })
    },
  },
}
</script>

<style lang="scss">
.occupation-container {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  * {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }

  .title {
    font-size: 32px;
    font-weight: 700;
    color: #3E3E3E;
  }

  .description {
    color: #616161;
    white-space: pre;
  }

  .flag-row {
    cursor: pointer;
    outline: none;
  }

  .flag-row[aria-disabled="true"] {
    cursor: default;
    pointer-events: none;
  }

  .flag-row[aria-disabled="true"] .flag-card {
    opacity: 0.6;
  }

  .flag-row[aria-disabled="true"] .check-img {
    opacity: 0.5;
  }

  .flag-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 1px 2px 4px 0 rgba(0, 0, 0, 0.1);

    .flag-title {
      font-size: 16px;
      font-weight: 600;
    }

    .flag-description {
      font-size: 16px;
      line-height: 1.2;
    }
  }

  .check-col {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
  }

  .check-img {
    width: 28px;
    height: 28px;
    object-fit: contain;
    user-select: none;
  }

  .flex-1 {
    flex: 1;
  }

  .btn-row-eq {
    display: flex;
    gap: 15px;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .btn-row-eq > .btn {
    flex: 1 1 0;
    min-width: 0;
  }

}
</style>
