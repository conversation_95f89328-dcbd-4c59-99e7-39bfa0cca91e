<template>
  <b-container class="kyc-steps-container">
    <b-row align-h="center">
      <b-row align-v="center" class="step-number-container">
        <div class="step-number d-flex align-items-center justify-content-center" :class="{ active: currentStep === 1 }">
          1<img v-if="currentStep > 1" src="@/assets/img/check-completed.svg" alt="" class="check-img"/>
        </div>
        <b-col class="dashed-line" :class="{ active: currentStep > 1 }"></b-col>

        <div class="step-number d-flex align-items-center justify-content-center" :class="{ active: currentStep === 2 }">
          2<img v-if="currentStep > 2" src="@/assets/img/check-completed.svg" alt="" class="check-img"/>
        </div>
        <b-col class="dashed-line" :class="{ active: currentStep > 2 }"></b-col>

        <div class="step-number d-flex align-items-center justify-content-center" :class="{ active: currentStep === 3 }">
          3<img v-if="currentStep > 3" src="@/assets/img/check-completed.svg" alt="" class="check-img"/>
        </div>
        <b-col class="dashed-line" :class="{ active: currentStep > 3 }"></b-col>

        <div class="step-number d-flex align-items-center justify-content-center" :class="{ active: currentStep === 4 }">
          4<img v-if="currentStep > 4" src="@/assets/img/check-completed.svg" alt="" class="check-img"/>
        </div>
        <b-col class="dashed-line" :class="{ active: currentStep > 4 }"></b-col>

        <div class="step-number d-flex align-items-center justify-content-center" :class="{ active: currentStep === 5 }">
          5<img v-if="currentStep > 5" src="@/assets/img/check-completed.svg" alt="" class="check-img"/>
        </div>
      </b-row>

      <!-- Labels row: same grid template as the top row -->
      <div class="labels-grid">
        <div class="label text-step-info" :class="{ active: currentStep === 1 }">
          {{ $t('account.OCCUPATION') }}
        </div>
        <div class="spacer"></div>

        <div class="label text-step-info" :class="{ active: currentStep === 2 }">
          {{ $t('account.ADDITIONAL_STATEMENT') }}
        </div>
        <div class="spacer"></div>

        <div class="label text-step-info" :class="{ active: currentStep === 3 }">
          {{ $t('account.DATA_CONFIRMATION') }}
        </div>
        <div class="spacer"></div>

        <div class="label text-step-info" :class="{ active: currentStep === 4 }">
          {{ $t('account.DATA_VERIFICATION') }}
        </div>
        <div class="spacer"></div>

        <div class="label text-step-info" :class="{ active: currentStep === 5 }">
          {{ $t('account.KYC_STATUS.DONE') }}
        </div>
      </div>
    </b-row>
  </b-container>
</template>

<script>
export default {
  name: 'KycStepper',
  props: {
    currentStep: {
      type: Number,
      default: 1,
    },
  },
};
</script>

<style lang="scss" scoped>
.kyc-steps-container {
  /* base circle size; used for grid column width */
  --num-size: 26px;
  /* grid template used by BOTH rows: num | line | num | line | num | line | num | line | num  */
  --step-template: var(--num-size) 1fr var(--num-size) 1fr var(--num-size) 1fr var(--num-size) 1fr var(--num-size);

  @media (max-width: 767px) {
    --num-size: 22px;
  }

  @media (max-width: 460px) {
    --num-size: 20px;
  }

  @media (max-width: 380px) {
    --num-size: 18px;
  }

  .step-number-container {
    width: 83%;
    display: grid;
    grid-template-columns: var(--step-template);
    align-items: center;
    column-gap: 2px;

    @media screen and (max-width: 767px) {
      width: 96%;
    }
  }

  .labels-grid {
    width: 83%;
    margin: 6px auto 0;
    display: grid;
    grid-template-columns: var(--step-template);
    align-items: start;
    column-gap: 2px;

    @media screen and (max-width: 767px) {
      width: 96%;
    }

    .label {
      grid-row: 1;               /* keep all labels on the same row */
      justify-self: center;      /* center under the number */
      text-align: center;
      white-space: normal;       /* allow wrapping */

      @media screen and (max-width: 767px) {
        font-size: 12px;
      }
    }
    .spacer {
      grid-row: 1;
    }
  }

  .step-number {
    width: var(--num-size);
    height: var(--num-size);
    border-radius: 50%;
    font-size: 16px;
    color: rgb(121, 121, 121);
    border: 1px solid rgb(121, 121, 121);
    position: relative;

    &.active {
      background-color: var(--primary-color);
      color: #fff;
      border-color: var(--primary-color);
    }

    &.scanning {
      background-color: #e39635;
      color: #fff;
      border-color: #e39635;
    }

    &.failed {
      background-color: red;
      color: #fff;
      border-color: red;
    }

    @media (max-width: 767px) {
      font-size: 14px;
    }

    @media (max-width: 380px) {
      font-size: 12px;
    }
  }

  .text-step-info {
    color: rgb(121, 121, 121);
    font-size: 15px;
    line-height: 1.2;

    &.active {
      color: #000;
    }

    @media (max-width: 460px) {
      font-size: 10px;
    }
  }

  .dashed-line {
    width: 100%;
    height: 1px;
    margin-right: 0;
    padding-right: 0;
    background: repeating-linear-gradient(
        to right,
        transparent,
        transparent 8px,
        rgb(121, 121, 121) 10px,
        rgb(121, 121, 121) 18px
    );


    @media screen and (max-width: 767px) {
      background: repeating-linear-gradient(
          to right,
          transparent,
          transparent 4px,
          rgb(121, 121, 121) 5px,
          rgb(121, 121, 121) 9px
      );
    }

    &.active {
      height: 2px;
      background: var(--primary-color) none;
    }
  }

  .check-img {
    position: absolute;
    width: 30px;
    height: 30px;
    object-fit: fill;

    @media (max-width: 767px) {
      width: 26px;
      height: 26px;
    }

    @media (max-width: 460px) {
      width: 24px;
      height: 24px;
    }

    @media (max-width: 380px) {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
