<template>
  <b-container class="kyc-status-container mt-3">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10">
        <div class="d-flex align-items-start justify-content-start kyc-status-content">
          <div class="cls-kyc-status-icon mr-3 mt-5">
            <img src="@/assets/img/parental-kyc/kyc-reviewed.png" alt="" class="icon-status">
          </div>
          <div class="cls-kyc-status">
            <h3 class="cls-headline font-28 mb-4 mt-0">
              {{ $t("account.DATA_VERIFICATION_STATUS") }}
            </h3>
            <div class="cls-verification-status d-flex flex-column align-items-start justify-content-start">
              <div class="cls-status-item step-1 d-flex flex-row align-items-start justify-content-start" :class="getStatusClassForStep(1)">
                <div class="cls-status-step d-flex flex-column align-items-center justify-content-start">
                  <p class="cls-icon-status">
                    <img width="24" src="@/assets/img/icons/kyc-process.svg" alt=""/>
                  </p>
                  <div class="cls-line-timeline-status">
                    <p class="cls-line-status" :class="getStatusClassForStep(1)">
                    </p>
                    <p class="cls-line-status" :class="getStatusClassForStep(2)">
                    </p>
                  </div>
                </div>
                <div class="cls-status-step-text">
                  <p class="cls-step-text font-20 mb-1">
                    {{ $t("account.KYC_STATUS.VERIFICATION_PROCESS_HEADLINE") }}
                  </p>
                  <p v-if="beingReviewed && (!completedVerification && !failedVerification)" class="cls-step-detail font-16">
                    {{ $t("account.KYC_STATUS.VERIFICATION_PROCESS_TEXT") }}
                  </p>
                </div>
              </div>
              <div class="cls-status-item step-2 d-flex flex-row align-items-start justify-content-start" :class="getStatusClassForStep(2)">
                <div class="cls-status-step d-flex flex-column align-items-center justify-content-start">
                  <p class="cls-icon-status">
                    <img v-if="!failedVerification" width="24" height="24" src="@/assets/img/icons/kyc-success.svg" alt=""/>
                    <img v-if="failedVerification && !completedVerification" width="24" height="24" src="@/assets/img/icons/kyc-failed.svg" alt=""/>
                  </p>
                </div>
                <div class="cls-status-step-text">
                  <p v-if="!failedVerification" class="cls-step-text">
                    {{ $t("account.KYC_STATUS.VERIFICATION_SUCCESSFUL_HEADLINE") }}
                  </p>
                  <p v-if="failedVerification && !completedVerification" class="cls-step-text">
                    {{ $t("account.KYC_STATUS.VERIFICATION_FAILED_HEADLINE") }}
                  </p>
                  <p v-if="failedVerification && !completedVerification" class="cls-step-detail">
                    {{ $t("account.KYC_STATUS.VERIFICATION_FAILED_TEXT") }} <a :href="waSupport" target="_blank" class="cls-wa-support">{{ phoneSupport }}</a>
                  </p>
                </div>
              </div>
            </div>
            <div class="cls-actions">
              <button v-if="!completedVerification && !failedVerification" id="id_kyc_status_refresh_btn" class="btn btn-secondary btn btn-none btn-main font-18 w-100 mt-5 mb-4"
                      @click="refreshKycStatus()">
                {{ $t("account.KYC_STATUS.REFRESH") }}
              </button>
              <button v-if="completedVerification || failedVerification" id="id_kyc_status_done_btn" class="btn btn-secondary btn btn-none btn-main font-18 w-100 mt-5 mb-4" @click="completeKyc()">
                {{ $t("account.KYC_STATUS.DONE") }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </b-container>
</template>

<script>
import externalSites from '@/constants/externalSites';
import { isFullyActive } from "@/constants/userStatus";

export default {
  components: {},
  data() {
    return {
      statusClass: {
        inqueue: 'inqueue',
        reviewing: 'reviewing',
        passed: 'passed',
        failed: 'failed'
      },
      phoneSupport: '',
      waSupport: ''
    }
  },

  mounted() {
    this.phoneSupport = externalSites.PHONE_SUPPORTS[this.$i18n.locale]
    this.waSupport = externalSites.WHATSAPP_SUPPORTS[this.$i18n.locale]
  },

  methods: {
    getStatusClassForStep(step = 1) {
      if (!this.beingReviewed && !this.completedVerification && !this.failedVerification) {
        return this.statusClass.inqueue
      }
      if (step === 1) {
        if (this.beingReviewed && !this.completedVerification && !this.failedVerification) {
          return this.statusClass.reviewing
        }
        if (this.completedVerification || this.failedVerification) {
          return this.statusClass.passed
        }
      }

      if (step === 2) {
        if (this.beingReviewed && !this.completedVerification && !this.failedVerification) {
          return this.statusClass.inqueue
        }
        if (this.completedVerification && !this.failedVerification) {
          return this.statusClass.passed
        }
        if (this.failedVerification) {
          return this.statusClass.failed
        }
      }
      return this.statusClass.inqueue
    },
    async completeKyc() {
      let redirectTo = this.$router.resolve({ name: 'marketplace' }).href
      if (this.$route.query && this.$route.query.redirect) {
        redirectTo = this.$router.resolve(this.$route.query.redirect).href
      }
      await this.$router.push(redirectTo)
    },
    async refreshKycStatus() {
      await this.$store.dispatch("setIsLoading", true);
      await this.$store.dispatch('refreshUserProfile');
      await this.$store.dispatch("setIsLoading", false);
    },
  },

  computed: {
    user() {
      return this.$store.getters.userProfile;
    },
    completedVerification() {
      return (this.user.kyc_status && this.user.kyc_status.completedVerification) || isFullyActive(true);
    },

    beingReviewed() {
      return this.user.kyc_status && this.user.kyc_status.beingReviewed;
    },

    failedVerification() {
      return this.user.kyc_status && this.user.kyc_status.failedVerification;
    },
  }
}
</script>

<style lang="scss" scoped>
.kyc-status-container {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  * {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }

  .kyc-status-content {
    @media(max-width: 991px) {
      flex-direction: column;
    }

    .cls-kyc-status-icon {
      width: 332px;
      text-align: center;

      .icon-status {
        width: 100%;
        height: auto;
      }

      @media(max-width: 991px) {
        width: 100%;
        margin-top: 0 !important;
        margin-right: 0 !important;
        .icon-status {
          width: 80%;
        }
      }
    }

    .cls-kyc-status {
      flex: 1;

      .cls-headline {
        font-weight: 600;
        line-height: 38px;
        letter-spacing: 0;
        text-align: center;
        color: #333333;
      }

      .cls-verification-status {
        .cls-status-item {
          .cls-status-step {
            margin-right: 20px;

            .cls-icon-status {
              width: 50px;
              height: 50px;
              background-color: #FF7C03;
              border-radius: 100%;
              display: flex;
              justify-content: center;
              align-items: center;

              img {
                width: 24px;
              }
            }

            .cls-line-status {
              min-height: 45px;
              width: 2px;
              border-left: 2px solid #BABABA;

              &.inqueue {
                border-left: 2px solid #BABABA;
              }

              &.reviewing {
                border-left: 2px solid #FF7C03;
              }

              &.passed {
                border-left: 2px solid #006867;
              }

              &.failed {
                border-left: 2px solid #FB234A;
              }
            }
          }

          .cls-status-step-text {
            margin-top: 12px;

            .cls-step-text {
              font-weight: 600;
              line-height: 28.06px;
              color: #FF7C03;
            }

            .cls-step-detail {
              font-weight: 382;
              line-height: 22.45px;
              color: #FF7C03;

              .cls-wa-support {
                color: #006867;
                text-decoration: underline;

                &:hover {
                  opacity: .8;
                }
              }
            }
          }

          &.inqueue {
            .cls-status-step {
              .cls-icon-status {
                background-color: #BABABA;
              }
            }

            .cls-status-step-text {
              .cls-step-text {
                color: #BABABA;
              }

              .cls-step-detail {
                color: #BABABA;
              }
            }
          }

          &.reviewing {
            .cls-status-step {
              .cls-icon-status {
                background-color: #FF7C03;
              }
            }

            .cls-status-step-text {
              .cls-step-text {
                color: #FF7C03;
              }

              .cls-step-detail {
                color: #FF7C03;
              }
            }
          }

          &.passed {
            .cls-status-step {
              .cls-icon-status {
                background-color: #006867;
              }
            }

            .cls-status-step-text {
              .cls-step-text {
                color: #333333;
              }

              .cls-step-detail {
                color: #333333;
              }
            }
          }

          &.failed {
            .cls-status-step {
              .cls-icon-status {
                background-color: #FB234A;
              }
            }

            .cls-status-step-text {
              .cls-step-text {
                color: #FB234A;
              }

              .cls-step-detail {
                color: #FB234A;
              }
            }
          }
        }
      }
    }
  }
}
</style>