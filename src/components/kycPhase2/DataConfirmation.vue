<template>
  <b-container class="confirm-container cls-kyc-form-container">
    <b-row align-h="center">
      <b-col cols="12" class="text-center">
        <p class="title mt-4">
          {{ $t('account.DATA_CONFIRMATION') }}
        </p>
        <p class="subtitle mt-2">
          {{ $t('account.DATA_CONFIRMATION_DESCRIPTION') }}
        </p>
      </b-col>
    </b-row>

    <b-row align-h="center" class="mt-4">
      <b-col cols="12" class="confirm-box">
        <ul class="kv-list">

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.OCCUPATION') }}</div>
            <div class="kv-value">{{ getName(occupations, modelValue.occupation_id) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.JOB_POSITION') }}</div>
            <div class="kv-value">{{ getName(jobPositions, modelValue.job_position_id) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.MONTHLY_INCOME') }}</div>
            <div class="kv-value">{{ getName(monthlyIncomes, modelValue.monthly_income_id) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.SOURCE_OF_INCOME') }}</div>
            <div class="kv-value">{{ getName(sourceOfIncomes, modelValue.source_of_income_id) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.COMPANY_BUSINESS_NAME') }}</div>
            <div class="kv-value">{{ modelValue.company_name || '-' }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.COMPANY_BUSINESS_ADDRESS') }}</div>
            <div class="kv-value">{{ modelValue.company_address || '-' }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">{{ $t('account.INDUSTRY') }}</div>
            <div class="kv-value">{{ getName(industrySectors, modelValue.industry_sector_id) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">
              {{ $t('account.POLITICALLY_EXPOSED_PERSON_DESCRIPTION_2') }}
            </div>
            <div class="kv-value">{{ yesNo(pepSelected) }}</div>
          </li>

          <li class="kv-item">
            <div class="kv-label">
              {{ $t('account.BENEFICIAL_OWNER_DESCRIPTION_2') }}
            </div>
            <div class="kv-value">{{ yesNo(boSelected) }}</div>
          </li>

        </ul>
      </b-col>
    </b-row>

    <div class="btn-row-eq mt-4 mb-2">
      <b-button class="btn-outline-main px-4" @click="$emit('previous-step')" variant="none">
        {{ $t('common.PREVIOUS') }}
      </b-button>
      <b-button class="btn-main px-4" variant="none" @click="$emit('next-step')">
        {{ $t('common.NEXT') }}
      </b-button>
    </div>
  </b-container>
</template>

<script>
import { KYC_PHASE_2_FLAGS } from "@/constants/constants";

export default {
  name: 'KycPhase2Confirm',
  emits: ['previous-step', 'next-step'],
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        occupation_id: null,
        job_position_id: null,
        monthly_income_id: null,
        source_of_income_id: null,
        company_name: null,
        company_address: null,
        industry_sector_id: null,
        flags: [],
      }),
    },
    occupations: { type: Array, default: () => [] },
    jobPositions: { type: Array, default: () => [] },
    monthlyIncomes: { type: Array, default: () => [] },
    sourceOfIncomes: { type: Array, default: () => [] },
    industrySectors: { type: Array, default: () => [] },
  },

  computed: {
    pepSelected() {
      return Array.isArray(this.modelValue.flags) && this.modelValue.flags.includes(KYC_PHASE_2_FLAGS.PEP)
    },
    boSelected() {
      return Array.isArray(this.modelValue.flags) && this.modelValue.flags.includes(KYC_PHASE_2_FLAGS.BO)
    },
  },

  methods: {
    getName(list, id) {
      if (!Array.isArray(list) || id == null) return '-'
      const found = list.find(x => x.id === id)
      return found?.name || '-'
    },
    yesNo(v) {
      return v ? this.$t('account.YES') : this.$t('account.NO')
    },
  },
}
</script>

<style lang="scss">
.confirm-container {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  * {
    font-family: inherit;
  }

  .title {
    font-size: 32px;
    font-weight: 700;
    color: #3E3E3E;
    margin-bottom: 0;
  }

  .subtitle {
    color: #616161;
    white-space: pre;
    margin-top: 6px;
    margin-bottom: 0;
    font-size: 18px;
  }

  .confirm-box {
    max-width: 520px;
  }

  .kv-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .kv-item {
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .kv-label {
    color: #616161;
    font-weight: 600;
    margin-bottom: 2px;
  }

  .kv-value {
    color: #A2A2A2;
  }

  .btn-row-eq {
    display: flex;
    gap: 12px;
    width: 100%;
    max-width: 320px; /* keep buttons centered & tidy */
    margin: 0 auto;
  }

  .btn-row-eq > .btn {
    flex: 1 1 0;
    min-width: 0;
  }
}
</style>
