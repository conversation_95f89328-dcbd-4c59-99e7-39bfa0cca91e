<template>
    <b-container class="upload-container cls-kyc-form-container">
        <b-row align-h="center">
            <b-col cols="12" lg="11">
                <div class="d-flex flex-column flex-lg-row">
                    <b-col cols="12" lg="6" class="text-center w-100 mt-2 mt-lg-5 cls-info-block">
                        <img width="122" src="@/assets/img/new-info-person.png" alt="" class="icon-img">
                        <p class="font-28 font-weight-bold title">{{ uploadTitle }}</p>
                        <p class="complete-info content-text font-16 description">{{ uploadDescription }}</p>
                    </b-col>
                    <b-col v-if="!isPendingCard && step === 1" cols="12" lg="6" class="mt-3 w-100 cls-sub-info-block">
                        <p v-if="(!isPendingCard || typeSelected) && !success" class="font-18 sub-title">{{
                            $t('account.ARE_YOU_AN_INDONESIAN') }}
                        </p>
                        <p v-if="(!isPendingCard || typeSelected) && !success" class="font-14 content-text sub-description">{{
                            $t('account.DO_YOU_HOLD_KTP') }}</p>
                        <div v-if="(!isPendingCard || typeSelected) && !success" class="d-flex flex-row justify-content-start mt-2">
                            <div>
                                <input id="radio-yes" type="radio" v-model="typeSelected" value="YES" @click="clearOldInfo" />
                                <label :style="{ color: typeSelected === 'YES' ? '#006666' : '#666' }" for="radio-yes">
                                    {{ $t('account.YES').toUpperCase() }}
                                </label>
                            </div>

                            <div class="ml-5">
                                <input id="radio-no" type="radio" v-model="typeSelected" value="NO" @click="clearOldInfo" />
                                <label :style="{ color: typeSelected === 'NO' ? '#006666' : '#666' }" for="radio-no">
                                    {{ $t('account.NO').toUpperCase() }}
                                </label>
                            </div>
                        </div>
                        <p v-if="typeSelected && !isPendingCard" class="font-18 mt-2 mb-2 sub-title">{{ title }}</p>

                        <div v-if="(selectedBase64 || typeSelected) && !isPendingCard" class="drop-area text-center"
                            @dragover.prevent @drop="onDrop">
                            <label v-if="!selectedBase64" class="btn-select font-16">
                                {{ uploading ? '' : typeSelected === 'NO' ? $t('account.CLICK_TO_UPLOAD_PASSPORT')
                                    : $t('account.CLICK_TO_UPLOAD_KTP') }}
                                <input type="file" name="image" @change="onChange" accept="image/png, image/jpeg, image/bmp" />
                            </label>
                            <div v-else class="upload-hidden text-center image">
                                <img :src="selectedBase64" alt="" class="card-img" />
                            </div>
                            <b-icon v-if="selectedBase64 && !uploading" class="delete-icon" icon="x-circle"
                                @click="removeFile"></b-icon>
                            <div v-if="uploading" class="overlay d-flex justify-content-center align-items-center">
                                <b-spinner variant="success" class="spinner" type="grow" label="Spinning"></b-spinner>
                            </div>
                        </div>
                        <img v-if="cardImageUrl && !selectedBase64" :src="cardImageUrl" class="card-img" />
                        <p v-if="message" class="note" :class="{ error: hasError }">{{
                            message }}</p>
                    </b-col>
                    <b-col v-else cols="12" lg="6" class="mt-3 w-100">
                        <div class="d-flex flex-column flex-lg-row align-items-center align-items-lg-start">
                            <b-col cols="12" lg="6" class="p-2 d-flex flex-column align-items-center">
                                <div class="d-flex justify-content-center mb-2">
                                    <img src="@/assets/img/check-completed.svg" alt="" class="img-correct-wrong">
                                    <p class="font-weight-bold ml-2">{{ $t('account.KYC_CORRECT_EXAMPLE') }}</p>
                                </div>
                                <img v-if="typeSelected === 'YES'" src="@/assets/img/kyc/correct-ktp.png" alt="" class="w-100">
                                <img v-else src="@/assets/img/kyc/correct-passport.png" alt="" class="w-100">
                                <p v-if="typeSelected === 'YES'" class="font-14 text-center mt-2 color-gray">{{ $t('account.KYC_ID_CARD_CORRECT_NOTE') }}</p>
                                <p v-else class="font-14 text-center mt-2 color-gray">{{ $t('account.PASSPORT_CORRECT_NOTE') }}</p>
                            </b-col>
                            <b-col cols="12" lg="6" class="p-2 d-flex flex-column align-items-center">
                                <div class="d-flex justify-content-center align-items-center mb-2">
                                    <img src="@/assets/img/wrong.png" alt="" class="img-correct-wrong">
                                    <p class="font-weight-bold ml-2">{{ $t('account.KYC_WRONG_EXAMPLE') }}</p>
                                </div>
                                <img v-if="typeSelected === 'YES'" src="@/assets/img/kyc/wrong-ktp.png" alt="" class="w-100">
                                <img v-else src="@/assets/img/kyc/wrong-passport.png" alt="" class="w-100">
                                <p v-if="typeSelected === 'YES'" class="font-14 text-center mt-2 color-gray">{{ $t('account.KYC_ID_CARD_WRONG_NOTE') }}</p>
                                <p v-else class="font-14 text-center mt-2 color-gray">{{ $t('account.PASSPORT_WRONG_NOTE') }}</p>
                            </b-col>
                        </div>
                    </b-col>
                </div>
            </b-col>
        </b-row>
        <div class="d-flex flex-row justify-content-end mt-4">
            <b-button id="btn_uploadCard_PreviousStep" v-if="step > 1 && !isPendingCard" :disabled="uploading" class="btn-outline-main px-4 mr-3" @click="previousStep" variant="none">{{ $t('common.PREVIOUS') }}</b-button>
            <b-button id="btn_uploadCard_Next" class="btn-main px-4" :disabled="!success && step >= 2" @click="nextStep" variant="none">{{
                $t('common.NEXT') }}
            </b-button>
        </div>
    </b-container>
</template>
<script>
import { getBase64, resizeBase64Img } from '../../helpers/common';
import accountService from "../../services/account.service";
import authService from "../../services/auth.service";
import { urlImage } from "../../helpers/common";
import store from '../../store/store';
import { Buffer } from 'buffer';

export default {
    emits: ['next-step'],

    data() {
        return {
            progress: null,
            error: null,
            interval: null,
            uploading: false,
            typeSelected: 'YES',
            status: store.state.userProfile.id_card && store.state.userProfile.id_card.status,
            message: null,
            cardImageUrl: urlImage({ image: store.state.userProfile.id_card && store.state.userProfile.id_card.card_image }),
            success: false,
            selectedBase64: null,
            hasError: false,
            step: 1,
        };
    },

    mounted() {
        if (this.isPendingCard) {
            this.message = this.$t('account.WE_ARE_PROCESSING_YOUR_CARD')
            this.step = 2
            this.startChecking()
        } else if (this.isCompletedCard && !this.isValid) {
            this.handleErrorFields(store.state.userProfile.id_card.card_data)
        }
    },

    methods: {
        async uploadFile(file) {
            this.hasError = false
            let base64 = await getBase64(file);
            this.selectedBase64 = base64
            let sizeInKB = file.size / 1024;
            if (sizeInKB > 2048) {
                base64 = await resizeBase64Img(base64);
                const buffer = Buffer.from(base64.substring(base64.indexOf(',') + 1));
                sizeInKB = buffer.length / 1024;
                if (sizeInKB > 2048) {
                    this.hasError = true
                    this.message = this.$t('account.IMAGE_SIZE_TOO_LARGE')
                    return;
                }
            } else if (sizeInKB < 110) {
                this.hasError = true
                this.message = this.$t('account.IMAGE_SIZE_TOO_SMALL')
                return;
            }

            try {
                this.message = this.$t('account.WE_ARE_PROCESSING_YOUR_CARD')
                this.uploading = true;
                const response = await accountService.uploadCard({
                    image: base64,
                    type: this.typeSelected === 'YES' ? 'indonesia_ktp' : 'passport',
                });
                const { data } = response;
                this.status = data.status;
                this.uploading = false;
                if (data.status === 'COMPLETED') {
                    if (data.is_valid) {
                        this.message = this.successMessage
                        this.success = true
                        this.hasError = false
                    } else {
                        this.handleErrorFields(data.card_data)
                    }
                } else if (data.status === 'FAILED') {
                    this.message = this.invalidMessage
                    this.hasError = true
                } else {
                    this.startChecking()
                }
            } catch (err) {
                this.uploading = false;
                this.message = err.data
                this.hasError = true
            }
        },

        startChecking() {
            this.interval = setInterval(async () => {
                const res = await authService.getUserProfile(false);
                if (res && res.data) {
                    const userProfile = res.data
                    this.status = userProfile.id_card.status;
                    await store.dispatch('setUserProfile', userProfile);
                    if (userProfile.id_card.status === 'COMPLETED') {
                        if (userProfile.id_card.is_valid) {
                            this.message = this.successMessage
                            this.success = true
                            this.hasError = false
                        } else {
                            this.handleErrorFields(data.card_data)
                        }
                        clearInterval(this.interval);
                    } else if (userProfile.id_card.status === 'FAILED') {
                        this.message = this.invalidMessage
                        this.hasError = true
                    }
                }
            }, 5000);
        },

        onDrop(e) {
            e.stopPropagation();
            e.preventDefault();
            const files = e.dataTransfer.files;
            this.uploadFile(files[0]);
        },
        onChange(e) {
            const files = e.target.files;
            this.uploadFile(files[0]);
        },

        removeFile() {
            this.selectedBase64 = null;
            this.message = this.$t('account.KTP_SIZE_NOTE')
            this.hasError = false
        },

        handleErrorFields(cardData) {
            const keys = ['city', 'name', 'rtrw', 'gender', 'address', 'village', 'district',
                'idNumber', 'province', 'religion', 'bloodType', 'expiryDate', 'occupation',
                'nationality', 'maritalStatus', 'birthPlaceBirthday']
            let fields = []
            keys.forEach((value) => {
                if (!cardData[value]) {
                    fields.push(this.$t(`account.KYC_FIELDS.${value.toUpperCase()}`))
                }
            })
            if (fields.length) {
                this.message = this.$t('account.COULD_NOT_DETECT_SOME_INFO', { value: fields.join(', ') })
            } else {
                this.message = this.invalidMessage
            }
            this.hasError = true
        },

        clearOldInfo() {
            this.cardImageUrl = ''
            this.message = ''
        },

        nextStep() {
            if(this.success) {
                this.$emit('next-step');
            } else {
                this.step++
            }
        },

        previousStep() {
            if (this.step > 1) {
                this.step--
            }
        }
    },

    computed: {

        isKTP() {
            if (store.state.userProfile.id_card) {
                return store.state.userProfile.id_card.card_type === 'indonesia_ktp'
            }
            return this.typeSelected === 'YES';
        },

        title() {
            return this.isKTP ?
                this.$t('account.PLEASE_SELECT_YOUR_ID_CARD') :
                this.$t('account.PLEASE_SELECT_YOUR_PASSPORT');
        },

        successMessage() {
            return this.isKTP ?
                this.$t('account.SUCCESS_VERIFIED_ID_CARD') :
                this.$t('account.SUCCESS_VERIFIED_PASSPORT');
        },

        invalidMessage() {
            return this.isKTP ?
                this.$t('account.ID_CARD_INVALID') :
                this.$t('account.PASSPORT_INVALID');
        },

        isPendingCard() {
            return this.status === 'PENDING';
        },

        isCompletedCard() {
            return this.status === 'COMPLETED';
        },

        isValid() {
            return store.state.userProfile.id_card && store.state.userProfile.id_card.is_valid
        },

        uploadTitle() {
            if (this.step === 1) {
                return this.$t('account.UPLOAD_IDENTITY_CARD')
            }
            if (this.step === 2) {
                return this.$t('account.IDENTITY_CARD_PHOTO_GUIDE')
            }
            return this.$t('account.PASSPORT_PHOTO_GUIDE')
        },

        uploadDescription() {
            if (this.step === 1) {
                return this.$t('account.IDENTITY_CARD_PHOTO_GUIDE_DESC')
            }
            if (this.step === 2) {
                return this.$t('account.PASSPORT_PHOTO_GUIDE_DESC')
            }
            return this.$t('account.UPLOAD_YOUR_ID_CARD_PASSPORT')
        },
    },
}
</script>

<style lang="scss">
.upload-container {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    *{
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    }
    .content-text {
        color: #666;
    }

    .complete-info {
        white-space: pre;
    }

    .icon-img {
        width: 180px;
        height: 100px;
        object-fit: contain;
    }

    .overlay {
        position: absolute;
        background-color: rgba(120, 111, 111, 0.432);
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;

        .spinner {
            width: 3rem;
            height: 3rem;
        }
    }

    .img-correct-wrong {
        width: 30px;
        height: 30px;
        object-fit: contain;
    }

    .note {
        font-size: 15px;
        color: black;
        margin-top: 10px;

        &.error {
            color: red;
        }
    }

    [type="radio"]:checked,
    [type="radio"]:not(:checked) {
        position: absolute;
        left: -9999px;
    }

    [type="radio"]:checked+label,
    [type="radio"]:not(:checked)+label {
        position: relative;
        padding-left: 28px;
        cursor: pointer;
        line-height: 20px;
        display: inline-block;
        color: #666;
    }

    [type="radio"]:checked+label:before,
    [type="radio"]:not(:checked)+label:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 18px;
        height: 18px;
        border: 1px solid #666;
        border-radius: 100%;
        background: #fff;
    }

    [type="radio"]:checked+label:after,
    [type="radio"]:not(:checked)+label:after {
        content: '';
        width: 12px;
        height: 12px;
        background: var(--primary-color);
        position: absolute;
        top: 3px;
        left: 3px;
        border-radius: 100%;
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
    }

    [type="radio"]:not(:checked)+label:after {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    [type="radio"]:checked+label:after {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    .helper {
        height: 100%;
        display: inline-block;
        vertical-align: middle;
        width: 0;
    }

    input[type="file"] {
        opacity: 0;
        z-index: -1;
    }

    .card-img {
        border: 1px solid #f6f6f6;
        display: inline-block;
        height: 200px;
        width: 100%;
        object-fit: contain;
    }

    .drop-area {
        background-color: #fff;
        border: 2px dashed #ccc;
        border-radius: 2px;
        height: 200px;
        width: 100%;
        position: relative;
    }

    .upload-hidden {
        display: none !important;
    }

    .upload-hidden.image {
        display: inline-block !important;
    }

    .btn-select {
        border: 0;
        color: #666;
        cursor: pointer;
        display: inline-block;
        font-weight: 500;
        padding: 15px 35px;
        position: relative;
        margin-top: 60px;
        width: 100%;
    }

    .delete-icon {
        width: 20px;
        height: 20px;
        color: red;
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
    }
}
</style>
