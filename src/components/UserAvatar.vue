<template>
  <div v-if="userProfile" class="goro-user-avatar-component">
    <b-dropdown ref="dropdown" variant="link" toggle-class="text-decoration-none" no-caret class="sub-menu-al-right goro-dropdown-site-menu-custom goro-dropdown-user-avatar">
      <template #button-content>
        <div class="content d-flex flex-column flex-lg-row align-items-center" @click="showDropdown">
          <img v-if="avatarUrl" class="img-avatar mt-1" :src="avatarUrl" width="36"  alt=""/>
          <default-avatar v-else :width="36" :height="36"></default-avatar>
          <div class="ml-2 d-flex flex-column align-items-center align-items-lg-start justify-content-center">
            <p class="font-16 font-weight-bold main-color">{{ myProfileText }}</p>
            <p class="text-uuid">{{ $t('GORO_ID') }}: {{ uuid }}</p>
          </div>
        </div>
      </template>
      <b-dropdown-item v-if="!this.isInAdminPage && !isPartnerRole" class="sub-item" @click="openMyPortfolio">
        {{ $t('account.MY_PORTFOLIO') }}
      </b-dropdown-item>
      <b-dropdown-item v-if="!this.isInAdminPage && !isPartnerRole" class="sub-item" @click="basicInfo">
        {{ $t('account.BASIC_INFO') }}
      </b-dropdown-item>
      <b-dropdown-item v-if="!this.isInAdminPage && this.showKYC && !isPartnerRole" class="sub-item" @click="kyc">
        {{ $t('account.KYC') }}
      </b-dropdown-item>
      <b-dropdown-item v-if="!this.isInAdminPage && this.showKYCPhase2 && !isPartnerRole" class="sub-item" @click="kycPhase2">
        {{ $t('account.KYC') }}
      </b-dropdown-item>
      <b-dropdown-item class="sub-item" @click="logout">
        {{ $t('account.LOGOUT') }}
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>
<script>

import { urlImage } from "@/helpers/common"
import roles, { isAdmin, isPartner, isUser } from "../constants/roles"
import { KYC_PHASE_2_STATUS } from "@/constants/constants";
import DefaultAvatar from "../components/DefaultAvatar.vue"
import { isFullyActive } from "@/constants/userStatus";

export default {
  components: {
    DefaultAvatar,
  },

  emits: ['close-menu'],

  methods: {
    showDropdown() {
      if (this.$refs && this.$refs.dropdown) {
        this.$refs.dropdown.show()
      }
    },
    async openMyPortfolio() {
      await this.$router.push({ name: 'assetsOverview' });
    },
    async logout() {
      const isInAdminPage = this.isInAdminPage || isAdmin();
      const isInAccountPage = this.isInAccountPage || isUser();
      const isPartnerPage = isPartner()

      await store.dispatch('doLogout');
      if (isInAdminPage) {
        await this.$router.push({ name: 'loginAdmin' });
      } else if (isPartnerPage) {
        await this.$router.push({ name: 'loginPartner' });
      } else if (isInAccountPage) {
        await this.$router.push({ name: 'login' });
      }
      this.closeMenu()
    },
    async basicInfo() {
      await this.$router.push({ name: 'myAccount' });
      this.closeMenu()
    },
    async kyc() {
      await this.$router.push({ name: 'kyc' });
      this.closeMenu()
    },
    async kycPhase2() {
      await this.$router.push({ name: 'kycPhase2' });
      this.closeMenu()
    },

    closeMenu() {
      this.$emit('close-menu')
    },
  },
  computed: {
    avatarUrl() {
      const url = this.userProfile && this.userProfile.avatar_url
      return urlImage({ image: url })
    },
    uuid() {
      return this.userProfile && this.userProfile.uuid
    },
    userProfile() {
      return this.$store.getters.userProfile;
    },

    isInAdminPage() {
      return this.$route.path && this.$route.path.includes('goroadmin');
    },

    isInAccountPage() {
      return this.$route.path && this.$route.path.includes('account');
    },

    isPartnerRole() {
      return isPartner()
    },

    isUser() {
      return this.$store.getters.userProfile.role === roles.User;
    },

    myProfileText() {
      return this.isUser ? this.$t('account.MY_ACCOUNT') : this.$t('account.MANAGE');
    },

    showKYC() {
      /**
       * Only show the menu KYC if:
       * - Or user had processed (!this.userProfile.kyc_status.hasNotKyc) KYC but not completed: !this.userProfile.kyc_status.completedVerification
       */
      return this.userProfile &&
        this.userProfile.kyc_status &&
        !this.userProfile.kyc_status.hasNotKyc &&
        (!this.userProfile.kyc_status.completedVerification || isFullyActive(true))
		},

    showKYCPhase2() {
      return this.userProfile && this.userProfile.kyc_phase_2
        && this.userProfile.kyc_phase_2.status !== KYC_PHASE_2_STATUS.STATUS_COMPLETED
    },
  },
}
</script>
<style lang="scss">
.goro-dropdown-user-avatar{
  .content {
    padding: 3px;
    margin-top: -3px;

    .text-uuid {
      font-size: 12px;
      color: gray;
    }

    p {
      margin: 0;
      padding: 0;
    }
  }

  .img-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid var(--primary-color);
  }
  .dropdown-toggle{
    @media(max-width: 991px) {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}
</style>
